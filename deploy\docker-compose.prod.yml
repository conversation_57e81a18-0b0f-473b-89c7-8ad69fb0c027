services:
  frontend:
    image: tongnguyen/frontend:latest
    container_name: cnpm_frontend
    environment:
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://**************/api/}
      - NODE_ENV=${NODE_ENV:-production}
    expose:
      - "3000"
    depends_on:
      - backend
    networks:
      - cnpm_network
    restart: unless-stopped

  backend:
    image: tongnguyen/backend:latest
    container_name: cnpm_backend
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=8080
      - DB_HOST=${DB_HOST:-db-mysql}
      - DB_PORT=${DB_PORT:-3306}
      - DB_NAME=${DB_NAME:-DBDKKHAMBENH}
      - DB_USER=${DB_USER:-root}
      - DB_PASSWORD=${DB_PASSWORD:-123456}
      - DATABASE_URL=mysql://${DB_USER:-root}:${DB_PASSWORD:-123456}@${DB_HOST:-db-mysql}:${DB_PORT:-3306}/${DB_NAME:-DBDKKHAMBENH}
      - JWT_SECRET=${JWT_SECRET:-f2494045c8b328d58179bf1d44909b5225cac30f5fdbe6826be8373fc3ab2f087bc4ab1cf7bde8ffe45000e7a19f93326f61764e17b9a977284bce8eddaa612a}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS:-http://**************}
    expose:
      - "8080"
    depends_on:
      - db-mysql
    networks:
      - cnpm_network
    restart: unless-stopped

  nginx:
    image: nginx:latest
    container_name: cnpm_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - frontend
      - backend
    networks:
      - cnpm_network
    restart: unless-stopped

  db-mysql:
    image: mysql:8.0
    container_name: cnpm_mysql
    environment:
      - MYSQL_DATABASE=${DB_NAME:-DBDKKHAMBENH}
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD:-123456}
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - cnpm_network
    restart: always

volumes:
  db_data:

networks:
  cnpm_network:
    driver: bridge
